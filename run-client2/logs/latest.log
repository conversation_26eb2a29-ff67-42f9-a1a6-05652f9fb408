[16:01:24] [main/INFO] (FabricLoader/GameProvider) Loading Minecraft 1.20.1 with Fabric Loader 0.16.13
[16:01:24] [main/INFO] (FabricLoader) Loading 59 mods:
	- fabric-api 0.92.5+1.20.1
	- fabric-api-base 0.4.32+1802ada577
	- fabric-api-lookup-api-v1 1.6.37+1802ada577
	- fabric-biome-api-v1 13.0.14+1802ada577
	- fabric-block-api-v1 1.0.12+1802ada577
	- fabric-block-view-api-v2 1.0.3+924f046a77
	- fabric-blockrenderlayer-v1 1.1.42+1802ada577
	- fabric-client-tags-api-v1 1.1.3+1802ada577
	- fabric-command-api-v1 1.2.35+f71b366f77
	- fabric-command-api-v2 2.2.14+1802ada577
	- fabric-commands-v0 0.2.52+df3654b377
	- fabric-containers-v0 0.1.66+df3654b377
	- fabric-content-registries-v0 4.0.13+1802ada577
	- fabric-convention-tags-v1 1.5.6+1802ada577
	- fabric-crash-report-info-v1 0.2.20+1802ada577
	- fabric-data-attachment-api-v1 1.0.2+de0fd6d177
	- fabric-data-generation-api-v1 12.3.6+1802ada577
	- fabric-dimensions-v1 2.1.55+1802ada577
	- fabric-entity-events-v1 1.6.1+1c78457f77
	- fabric-events-interaction-v0 0.6.4+13a40c6677
	- fabric-events-lifecycle-v0 0.2.64+df3654b377
	- fabric-game-rule-api-v1 1.0.41+1802ada577
	- fabric-gametest-api-v1 1.2.15+1802ada577
	- fabric-item-api-v1 2.1.29+1802ada577
	- fabric-item-group-api-v1 4.0.14+1802ada577
	- fabric-key-binding-api-v1 1.0.38+1802ada577
	- fabric-keybindings-v0 0.2.36+df3654b377
	- fabric-lifecycle-events-v1 2.2.23+1802ada577
	- fabric-loot-api-v2 1.2.3+1802ada577
	- fabric-loot-tables-v1 1.1.47+9e7660c677
	- fabric-message-api-v1 5.1.10+1802ada577
	- fabric-mining-level-api-v1 2.1.52+1802ada577
	- fabric-model-loading-api-v1 1.0.4+1802ada577
	- fabric-models-v0 0.4.3+9386d8a777
	- fabric-networking-api-v1 1.3.13+13a40c6677
	- fabric-networking-v0 0.3.53+df3654b377
	- fabric-object-builder-api-v1 11.1.5+e35120df77
	- fabric-particles-v1 1.1.3+1802ada577
	- fabric-recipe-api-v1 1.0.23+1802ada577
	- fabric-registry-sync-v0 2.3.5+1802ada577
	- fabric-renderer-api-v1 3.2.2+1802ada577
	- fabric-renderer-indigo 1.5.3+85287f9f77
	- fabric-renderer-registries-v1 3.2.47+df3654b377
	- fabric-rendering-data-attachment-v1 0.3.39+92a0d36777
	- fabric-rendering-fluids-v1 3.0.29+1802ada577
	- fabric-rendering-v0 1.1.50+df3654b377
	- fabric-rendering-v1 3.0.9+1802ada577
	- fabric-resource-conditions-api-v1 2.3.9+1802ada577
	- fabric-resource-loader-v0 0.11.12+fb82e9d777
	- fabric-screen-api-v1 2.0.9+1802ada577
	- fabric-screen-handler-api-v1 1.3.32+1802ada577
	- fabric-sound-api-v1 1.0.14+1802ada577
	- fabric-transfer-api-v1 3.3.6+8dd72ea377
	- fabric-transitive-access-wideners-v1 4.3.2+1802ada577
	- fabricloader 0.16.13
	- java 17
	- minecraft 1.20.1
	- mixinextras 0.4.1
	- pokecobbleclaim 1.0.0
[16:01:24] [main/INFO] (FabricLoader/Mixin) SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/6a12aacc794f1078458433116e9ed42c1cc98096/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[16:01:24] [main/INFO] (FabricLoader/Mixin) Loaded Fabric development mappings for mixin remapper!
[16:01:24] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_17
[16:01:26] [main/INFO] (FabricLoader/MixinExtras|Service) Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[16:01:34] [Datafixer Bootstrap/INFO] (Minecraft) 188 Datafixer optimizations took 130 milliseconds
[16:01:36] [Render thread/INFO] (Minecraft) Environment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[16:01:36] [Render thread/ERROR] (Minecraft) Failed to verify authentication
com.mojang.authlib.exceptions.InvalidCredentialsException: Status: 401
	at com.mojang.authlib.exceptions.MinecraftClientHttpException.toAuthenticationException(MinecraftClientHttpException.java:56) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.fetchProperties(YggdrasilUserApiService.java:156) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.<init>(YggdrasilUserApiService.java:55) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService.createUserApiService(YggdrasilAuthenticationService.java:124) ~[authlib-4.0.43.jar:?]
	at net.minecraft.client.MinecraftClient.createUserApiService(MinecraftClient.java:733) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.minecraft.client.MinecraftClient.<init>(MinecraftClient.java:442) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.minecraft.client.main.Main.main(Main.java:211) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86) ~[dev-launch-injector-0.2.1+build.8.jar:?]
Caused by: com.mojang.authlib.exceptions.MinecraftClientHttpException: Status: 401
	at com.mojang.authlib.minecraft.client.MinecraftClient.readInputStream(MinecraftClient.java:85) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.minecraft.client.MinecraftClient.get(MinecraftClient.java:48) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.fetchProperties(YggdrasilUserApiService.java:129) ~[authlib-4.0.43.jar:?]
	... 9 more
[16:01:36] [Render thread/INFO] (Minecraft) Setting user: Player2
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Initializing PokeCobbleClaim mod
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registering client-side network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered client town jobs network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered ClientGlobalChunkSyncHandler network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (client-side)
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer client-side packet handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered client-side phone app network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registering server-side network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered town jobs network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered town request network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered GlobalChunkSyncHandler network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (server-side)
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer server-side packet handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered phone app network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Initializing server-side phone systems...
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Loaded 21 products from storage
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered server-side product network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Registered server-side player upgrade network handlers
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Server-side phone systems initialized successfully
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Reset all town and player data versions
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Found 1 individual town files, loading...
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Restored claim tool access for Unknown: true
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Loaded saved permission data for player Player2 from old system (rank: Mayor) in town yesssssssssssss
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ChunkOwnershipManager initialized
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ChunkPermissionEngine v2 initialized
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ClaimTagIntegration hooks established
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Invalidated permission cache for player 550e8400-e29b-41d4-a716-446655440002 due to rank change from OWNER to OWNER in town 35ffe9ce-e24f-4143-9c32-0b242db2126c
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Processed rank change for player 550e8400-e29b-41d4-a716-446655440002 in town 35ffe9ce-e24f-4143-9c32-0b242db2126c: OWNER -> OWNER
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Set rank Mayor for player 550e8400-e29b-41d4-a716-446655440002 in town yesssssssssssss
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Invalidated permission cache for player 550e8400-e29b-41d4-a716-446655440002 due to rank change from OWNER to OWNER in town 35ffe9ce-e24f-4143-9c32-0b242db2126c
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Processed rank change for player 550e8400-e29b-41d4-a716-446655440002 in town 35ffe9ce-e24f-4143-9c32-0b242db2126c: OWNER -> OWNER
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Set rank Mayor for player 550e8400-e29b-41d4-a716-446655440002 in town yesssssssssssss
[16:01:37] [Render thread/WARN] (pokecobbleclaim) Cannot apply setting to town 35ffe9ce-e24f-4143-9c32-0b242db2126c - town not found in TownManager or ClientTownManager
[16:01:37] [Render thread/WARN] (pokecobbleclaim) Cannot apply setting to town 35ffe9ce-e24f-4143-9c32-0b242db2126c - town not found in TownManager or ClientTownManager
[16:01:37] [Render thread/WARN] (pokecobbleclaim) Cannot apply setting to town 35ffe9ce-e24f-4143-9c32-0b242db2126c - town not found in TownManager or ClientTownManager
[16:01:37] [Render thread/WARN] (pokecobbleclaim) Cannot apply setting to town 35ffe9ce-e24f-4143-9c32-0b242db2126c - town not found in TownManager or ClientTownManager
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Saved settings to disk for town 35ffe9ce-e24f-4143-9c32-0b242db2126c
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Set 4 town settings for town 35ffe9ce-e24f-4143-9c32-0b242db2126c
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Restored town settings for town yesssssssssssss: {name=yesssssssssssss, description=😚, maxPlayers=100.0, isOpen=true}
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Applying persisted settings to town yesssssssssssss (ID: 35ffe9ce-e24f-4143-9c32-0b242db2126c)
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Settings in memory: 4 entries
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Loaded settings for town yesssssssssssss: {name=yesssssssssssss, description=😚, maxPlayers=100.0, isOpen=true}
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Town yesssssssssssss current image before applying settings: default
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Applying setting name = yesssssssssssss to town yesssssssssssss
[16:01:37] [Render thread/WARN] (pokecobbleclaim) Cannot apply setting to town 35ffe9ce-e24f-4143-9c32-0b242db2126c - town not found in TownManager or ClientTownManager
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Applying setting description = 😚 to town yesssssssssssss
[16:01:37] [Render thread/WARN] (pokecobbleclaim) Cannot apply setting to town 35ffe9ce-e24f-4143-9c32-0b242db2126c - town not found in TownManager or ClientTownManager
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Applying setting maxPlayers = 100.0 to town yesssssssssssss
[16:01:37] [Render thread/WARN] (pokecobbleclaim) Cannot apply setting to town 35ffe9ce-e24f-4143-9c32-0b242db2126c - town not found in TownManager or ClientTownManager
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Applying setting isOpen = true to town yesssssssssssss
[16:01:37] [Render thread/WARN] (pokecobbleclaim) Cannot apply setting to town 35ffe9ce-e24f-4143-9c32-0b242db2126c - town not found in TownManager or ClientTownManager
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Town yesssssssssssss final image after applying settings: default
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Applied 4 persisted settings to town yesssssssssssss
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Loaded town 'yesssssssssssss' with 1 players from 35ffe9ce-e24f-4143-9c32-0b242db2126c.json
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Restored 1 player-town relationships
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Loaded 1 towns from individual files
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Town data loading completed. Final town count in TownManager: 1
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Simple chunk protection handler initialized
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Simple chunk protection system initialized successfully - complex systems disabled
[16:01:37] [Render thread/INFO] (pokecobbleclaim) === Testing Simple Permission System ===
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ✓ Test 1: Unclaimed chunk logic verified (allows all actions)
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ✓ Test 2: Permission constants are correct
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ✓ Test 3: Permission name mapping works correctly
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MEMBER (Resident), permissionIndex 0
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MEMBER: [true, false, false, false, false, false, false, false]
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank VISITOR (Citizen), permissionIndex 0
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank VISITOR: [false, false, false, false, false, false, false, false]
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank OWNER (Mayor), permissionIndex 0
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank OWNER: [true, true, true, true, true, true, true, true]
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ✓ Test 4: Individual rank permissions work correctly (non-hierarchical)
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank null (null), permissionIndex 1
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Non-member permission result = true
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank null (null), permissionIndex 0
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Non-member permission result = false
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ✓ Test 5: Non-member permissions work correctly
[16:01:37] [Render thread/INFO] (pokecobbleclaim) === All Simple Permission System Tests PASSED ===
[16:01:37] [Render thread/INFO] (pokecobbleclaim) === Testing Individual Rank Permissions (Non-Hierarchical) ===
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MEMBER (Resident), permissionIndex 2
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MEMBER: [false, false, true, false, false, false, false, false]
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank VISITOR (Citizen), permissionIndex 2
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank VISITOR: [false, false, false, false, false, false, false, false]
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MODERATOR (Council), permissionIndex 2
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MODERATOR: [false, false, false, false, false, false, false, false]
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank OWNER (Mayor), permissionIndex 2
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank OWNER: [true, true, true, true, true, true, true, true]
[16:01:37] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ✓ Individual rank permissions test PASSED - only specified rank gets permission (plus admin ranks)
[16:01:37] [Render thread/INFO] (pokecobbleclaim) 🎉 ALL PERMISSION SYSTEM TESTS PASSED! The new simple system is working correctly.
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Claim tag sync handlers initialized
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Starting initialization...
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Registering server-side handler for CHUNK_CLAIM_REQUEST: pokecobbleclaim:chunk_claim_request
[16:01:37] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Successfully registered CHUNK_CLAIM_REQUEST handler
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Chunk claim packet handlers initialized
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Chunk claim sync handlers initialized
[16:01:37] [Render thread/INFO] (pokecobbleclaim) Claim tool selection sync handlers initialized
[16:01:37] [Render thread/INFO] (pokecobbleclaim) PokeCobbleClaim mod initialized successfully
[16:01:37] [Render thread/INFO] (Indigo) [Indigo] Registering Indigo renderer!
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initializing PokeCobbleClaim client
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering town keybinding
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering chunk boundary renderer
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Loaded user preferences from: pokecobbleclaim-user-preferences.json
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Client Permission Handler v2 temporarily disabled for debugging
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initialized client-side data managers for synchronization
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering sounds
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Successfully registered sound: pokecobbleclaim:notification.invite
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Successfully registered sound: pokecobbleclaim:ui.button.click
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered sounds on client side
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering client-side network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered ClientGlobalChunkSyncHandler network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (client-side)
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer client-side packet handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered client-side phone app network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering server-side network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered town jobs network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered town request network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered GlobalChunkSyncHandler network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (server-side)
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer server-side packet handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered phone app network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initializing client-side phone systems...
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered client-side product network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered client-side player upgrade network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Client-side phone systems initialized successfully
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered client-side network handlers
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initializing phone feature
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initializing app position manager
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initializing app position manager
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Setting up default app positions
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initialized 24 default app positions
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initializing app registry
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initializing app registry
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered 7 apps
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initializing phone texture manager
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initializing and registering phone notification overlay
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering phone notification overlay renderer
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering phone notification renderer
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered phone notification renderer
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering phone keybinding
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering phone keybinding
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Initialized phone feature
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registering Shape Visualizer Tool
[16:01:38] [Render thread/INFO] (pokecobbleclaim) Registered shape visualizer tool
[16:01:38] [Render thread/INFO] (pokecobbleclaim) PokeCobbleClaim client initialized successfully
[16:01:38] [Render thread/INFO] (Minecraft) Backend library: LWJGL version 3.3.1 SNAPSHOT
[16:01:40] [Render thread/INFO] (Minecraft) Reloading ResourceManager: vanilla, fabric (fabric-sound-api-v1, fabric-resource-loader-v0, fabric-content-registries-v0, fabric-command-api-v1, fabric-block-view-api-v2, fabric-loot-api-v2, fabric-key-binding-api-v1, fabric-blockrenderlayer-v1, fabric-convention-tags-v1, fabric-model-loading-api-v1, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-keybindings-v0, fabric-data-generation-api-v1, fabric-models-v0, fabric-dimensions-v1, fabric-client-tags-api-v1, fabric-particles-v1, fabric-message-api-v1, fabric-loot-tables-v1, fabric-networking-v0, fabric-item-group-api-v1, fabric-rendering-data-attachment-v1, fabric-transfer-api-v1, fabric-events-interaction-v0, fabric-rendering-fluids-v1, fabric-object-builder-api-v1, fabric-screen-api-v1, fabric-game-rule-api-v1, fabric-screen-handler-api-v1, fabric-recipe-api-v1, fabric-rendering-v1, fabric-api, fabric-block-api-v1, fabric-rendering-v0, fabric-biome-api-v1, fabric-mining-level-api-v1, fabric-resource-conditions-api-v1, fabric-transitive-access-wideners-v1, fabric-api-base, fabric-item-api-v1, fabric-registry-sync-v0, fabric-entity-events-v1, fabric-networking-api-v1, fabric-containers-v0, fabric-renderer-registries-v1, fabricloader, fabric-gametest-api-v1, fabric-commands-v0, fabric-api-lookup-api-v1, fabric-events-lifecycle-v0, fabric-command-api-v2, fabric-lifecycle-events-v1, pokecobbleclaim)
[16:01:40] [Worker-Main-11/INFO] (Minecraft) Found unifont_all_no_pua-15.0.06.hex, loading
[16:01:40] [Realms Notification Availability checker #1/INFO] (Minecraft) Could not authorize you against Realms server: java.lang.RuntimeException: Failed to parse into SignedJWT: FabricMC
[16:01:42] [Render thread/WARN] (Minecraft) Missing sound for event: minecraft:item.goat_horn.play
[16:01:42] [Render thread/WARN] (Minecraft) Missing sound for event: minecraft:entity.goat.screaming.horn_break
[16:01:42] [Render thread/INFO] (Minecraft) OpenAL initialized on device Sound Blaster GC7 Analog Stereo
[16:01:42] [Render thread/INFO] (Minecraft) Sound engine started
[16:01:42] [Render thread/INFO] (Minecraft) Created: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[16:01:42] [Render thread/INFO] (Minecraft) Created: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[16:01:42] [Render thread/INFO] (Minecraft) Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[16:01:42] [Render thread/INFO] (Minecraft) Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[16:01:42] [Render thread/INFO] (Minecraft) Created: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[16:01:42] [Render thread/INFO] (Minecraft) Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[16:01:42] [Render thread/INFO] (Minecraft) Created: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[16:01:42] [Render thread/INFO] (Minecraft) Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[16:01:42] [Render thread/INFO] (Minecraft) Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[16:01:43] [Render thread/WARN] (Minecraft) Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[16:01:43] [Render thread/INFO] (Minecraft) Created: 256x256x0 minecraft:textures/atlas/particles.png-atlas
[16:01:43] [Render thread/INFO] (Minecraft) Created: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[16:01:43] [Render thread/INFO] (Minecraft) Created: 128x128x0 minecraft:textures/atlas/mob_effects.png-atlas
