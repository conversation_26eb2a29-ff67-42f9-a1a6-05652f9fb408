
> Configure project :
Fabric Loom: 1.10.5
"Lock for cache='/home/<USER>/.gradle/caches/fabric-loom', project=':'" is currently held by pid '53506'.
Run with --info or --debug to show arguments, may reveal sensitive info
- pid: 53506
- command: /usr/lib/jvm/java-17-openjdk-amd64/bin/java
- started at: 2025-08-08T19:51:08.200Z
- user: sika
- parent:
	- pid: 5794
	- started at: 2025-08-08T19:45:04Z
	- user: sika
	- parent:
		- pid: 1
		- started at: 2025-08-08T19:44:38.170Z
		- user: root
Waiting for lock to be released...

> Task :compileJava
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/claim/test/ClaimToolSystemTest.java:205: warning: [deprecation] handleTagKey() in UnifiedClaimInputHandler has been deprecated
            boolean result3 = handler.handleTagKey();
                                     ^
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/claim/ClaimTool.java:86: warning: [deprecation] toggleTagOnCurrentChunk() in SimplifiedClaimTool has been deprecated
        delegate.toggleTagOnCurrentChunk();
                ^
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/client/ClaimToolDataSynchronizer.java:80: warning: [unchecked] unchecked cast
                Map<String, Object> eventData = (Map<String, Object>) data;
                                                                      ^
  required: Map<String,Object>
  found:    Object
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:162: warning: [unchecked] unchecked cast
            return (Map<String, Object>) categoryPrefs;
                                         ^
  required: Map<String,Object>
  found:    Object
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:213: warning: [unchecked] unchecked conversion
            Map<String, Object> preferences = gson.fromJson(jsonObject, Map.class);
                                                           ^
  required: Map<String,Object>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:516: warning: [unchecked] unchecked conversion
            Map<String, Object> config = gson.fromJson(jsonValue, Map.class);
                                                      ^
  required: Map<String,Object>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:60: warning: [unchecked] unchecked conversion
            Map<String, Map<String, Object>> loaded = gson.fromJson(reader, Map.class);
                                                                   ^
  required: Map<String,Map<String,Object>>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:189: warning: [unchecked] unchecked cast
            return (T) value;
                       ^
  required: T
  found:    Object
  where T is a type-variable:
    T extends Object declared in method <T>getPreference(String,String,T)
8 warnings

> Task :processResources UP-TO-DATE
> Task :classes
> Task :jar UP-TO-DATE
> Task :compileTestJava UP-TO-DATE
> Task :processIncludeJars UP-TO-DATE
> Task :sourcesJar UP-TO-DATE
> Task :remapSourcesJar UP-TO-DATE
> Task :processTestResources NO-SOURCE
> Task :testClasses UP-TO-DATE
> Task :test
> Task :validateAccessWidener NO-SOURCE
> Task :check
> Task :cleanRunClient1
> Task :generateLog4jConfig UP-TO-DATE
> Task :generateRemapClasspath UP-TO-DATE
> Task :generateDLIConfig UP-TO-DATE
> Task :configureLaunch UP-TO-DATE
> Task :downloadAssets UP-TO-DATE
> Task :configureClientLaunch UP-TO-DATE
> Task :remapJar FAILED

[Incubating] Problems report is available at: file:///home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/build/reports/problems/problems-report.html

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':remapJar'.
> A failure occurred while executing net.fabricmc.loom.task.RemapJarTask$RemapAction
   > Failed to remap, java.nio.file.NoSuchFileException: /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/build/libs/pokecobbleclaim-1.0.0.jar.tmp

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.12.1/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD FAILED in 8s
14 actionable tasks: 4 executed, 10 up-to-date
