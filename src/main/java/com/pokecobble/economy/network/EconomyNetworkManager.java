package com.pokecobble.economy.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import com.pokecobble.economy.core.PlayerEconomyData;
import com.pokecobble.economy.manager.EconomyManager;

import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;

import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Manages network communication for the economy system.
 * Handles secure synchronization of economy data between server and clients.
 */
public class EconomyNetworkManager {

    // Network identifiers for economy packets
    public static final Identifier ECONOMY_BALANCE_SYNC = new Identifier("pokecobbleclaim", "economy_balance_sync");
    public static final Identifier ECONOMY_BALANCE_REQUEST = new Identifier("pokecobbleclaim", "economy_balance_request");
    public static final Identifier ECONOMY_TRANSACTION_NOTIFY = new Identifier("pokecobbleclaim", "economy_transaction_notify");
    public static final Identifier ECONOMY_DATA_SYNC = new Identifier("pokecobbleclaim", "economy_data_sync");
    public static final Identifier ECONOMY_TRANSFER_REQUEST = new Identifier("pokecobbleclaim", "economy_transfer_request");
    public static final Identifier ECONOMY_FOOD_SALE = new Identifier("pokecobbleclaim", "economy_food_sale");
    public static final Identifier ECONOMY_TRANSFER_RESULT = new Identifier("pokecobbleclaim", "economy_transfer_result");

    private final EconomyManager economyManager;
    private final ExecutorService networkExecutor;

    // Track player data versions for efficient synchronization
    private final Map<UUID, Integer> playerDataVersions;

    // Track players watching their balance
    private final Map<UUID, Boolean> watchingPlayers;

    private boolean initialized;
    // Lightweight server-side throttling and validation (no heavy auth)
    private static final int TRANSFER_COOLDOWN_MS = 750;      // 0.75s between transfers per player
    private static final int FOOD_SALE_COOLDOWN_MS = 750;     // 0.75s between food sales per player
    private static final int MAX_SALE_QUANTITY = 640;         // e.g., up to 10 stacks

    // Track last action timestamps per player
    private final Map<UUID, Long> lastTransferAt = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastFoodSaleAt = new ConcurrentHashMap<>();

    private boolean isOnCooldown(Map<UUID, Long> lastActionMap, UUID playerId, int windowMs) {
        long now = System.currentTimeMillis();
        Long last = lastActionMap.get(playerId);
        return last != null && (now - last) < windowMs;
    }

    private void markAction(Map<UUID, Long> lastActionMap, UUID playerId) {
        lastActionMap.put(playerId, System.currentTimeMillis());
    }


    /**
     * Creates a new economy network manager.
     *
     * @param economyManager The economy manager instance
     */
    public EconomyNetworkManager(EconomyManager economyManager) {
        this.economyManager = economyManager;
        this.networkExecutor = Executors.newFixedThreadPool(EconomyConfig.NETWORK_THREAD_POOL_SIZE);
        this.playerDataVersions = new ConcurrentHashMap<>();
        this.watchingPlayers = new ConcurrentHashMap<>();
        this.initialized = false;
    }

    /**
     * Initializes the network manager.
     */
    public void initialize() {
        if (initialized) {
            return;
        }

        try {
            // Register server-side handlers
            registerServerHandlers();

            this.initialized = true;
            Pokecobbleclaim.LOGGER.info("Economy network manager initialized successfully");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize economy network manager", e);
            throw new RuntimeException("Economy network manager initialization failed", e);
        }
    }

    /**
     * Shuts down the network manager.
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }

        try {
            // Shutdown network executor
            networkExecutor.shutdown();

            // Clear tracking data
            playerDataVersions.clear();
            watchingPlayers.clear();

            this.initialized = false;
            Pokecobbleclaim.LOGGER.info("Economy network manager shut down successfully");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during economy network manager shutdown", e);
        }
    }

    /**
     * Registers server-side packet handlers.
     */
    private void registerServerHandlers() {
        // Balance request handler
        ServerPlayNetworking.registerGlobalReceiver(ECONOMY_BALANCE_REQUEST, this::handleBalanceRequest);

        // Transfer request handler
        ServerPlayNetworking.registerGlobalReceiver(ECONOMY_TRANSFER_REQUEST, this::handleTransferRequest);

        // Food sale handler
        ServerPlayNetworking.registerGlobalReceiver(ECONOMY_FOOD_SALE, this::handleFoodSale);

        Pokecobbleclaim.LOGGER.info("Economy server-side network handlers registered");
    }

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Balance sync handler
        ClientPlayNetworking.registerGlobalReceiver(ECONOMY_BALANCE_SYNC, EconomyNetworkManager::handleBalanceSync);

        // Transaction notification handler
        ClientPlayNetworking.registerGlobalReceiver(ECONOMY_TRANSACTION_NOTIFY, EconomyNetworkManager::handleTransactionNotify);

        // Economy data sync handler
        ClientPlayNetworking.registerGlobalReceiver(ECONOMY_DATA_SYNC, EconomyNetworkManager::handleEconomyDataSync);

        // Transfer result handler
        ClientPlayNetworking.registerGlobalReceiver(ECONOMY_TRANSFER_RESULT, EconomyNetworkManager::handleTransferResult);

        Pokecobbleclaim.LOGGER.info("Economy client-side network handlers registered");
    }

    /**
     * Synchronizes player economy data to the client.
     *
     * @param player The player to synchronize to
     */
    public void syncPlayerData(ServerPlayerEntity player) {
        if (!initialized || player == null) {
            return;
        }

        networkExecutor.execute(() -> {
            try {
                UUID playerId = player.getUuid();
                PlayerEconomyData playerData = economyManager.getPlayerData(playerId);

                if (playerData == null) {
                    return;
                }

                // Check if data has changed since last sync
                int currentVersion = playerData.getDataVersion();
                Integer lastSyncedVersion = playerDataVersions.get(playerId);

                if (lastSyncedVersion != null && lastSyncedVersion == currentVersion) {
                    return; // No changes to sync
                }

                // Create sync packet
                PacketByteBuf buf = PacketByteBufs.create();

                // Write player data
                buf.writeUuid(playerId);
                buf.writeLong(playerData.getBalance());
                buf.writeLong(playerData.getTotalEarned());
                buf.writeLong(playerData.getTotalSpent());
                buf.writeInt(playerData.getTransactionCount());
                buf.writeInt(currentVersion);
                buf.writeLong(playerData.getLastUpdated());

                Pokecobbleclaim.LOGGER.debug("EconomyNetworkManager: Sending economy data sync for player " +
                                           player.getName().getString() + " - balance: " + playerData.getBalance() +
                                           ", version: " + currentVersion);

                // Skip authentication for now - focus on getting economy sync working
                // TODO: Fix packet authentication system
                // if (!PacketAuthenticator.authenticatePacket(buf, playerId)) {
                //     Pokecobbleclaim.LOGGER.warn("Failed to authenticate economy sync packet for player " + playerId);
                //     return;
                // }

                // Send packet
                NetworkManager.sendToPlayer(player, ECONOMY_DATA_SYNC, buf);

                // Update version tracking
                playerDataVersions.put(playerId, currentVersion);

                Pokecobbleclaim.LOGGER.debug("Synchronized economy data for player " + player.getName().getString());

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error synchronizing economy data for player " + player.getName().getString(), e);
            }
        });
    }

    /**
     * Synchronizes balance to a specific player.
     *
     * @param player The player to synchronize to
     */
    public void syncBalance(ServerPlayerEntity player) {
        if (!initialized || player == null) {
            return;
        }

        networkExecutor.execute(() -> {
            try {
                UUID playerId = player.getUuid();
                PlayerEconomyData playerData = economyManager.getPlayerData(playerId);

                if (playerData == null) {
                    return;
                }

                // Create balance sync packet
                PacketByteBuf buf = PacketByteBufs.create();

                buf.writeUuid(playerId);
                buf.writeLong(playerData.getBalance());
                buf.writeInt(playerData.getDataVersion());
                buf.writeLong(System.currentTimeMillis());

                Pokecobbleclaim.LOGGER.debug("EconomyNetworkManager: Sending balance sync for player " +
                                           player.getName().getString() + " - balance: " + playerData.getBalance() +
                                           ", version: " + playerData.getDataVersion());

                // Skip authentication for now - focus on getting balance sync working
                // TODO: Fix packet authentication system

                // Send packet
                NetworkManager.sendToPlayer(player, ECONOMY_BALANCE_SYNC, buf);

                Pokecobbleclaim.LOGGER.debug("Synchronized balance for player " + player.getName().getString() +
                                           ": " + playerData.getBalance());

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error synchronizing balance for player " + player.getName().getString(), e);
            }
        });
    }

    /**
     * Notifies a player about a transaction.
     *
     * @param player The player to notify
     * @param transactionType The type of transaction
     * @param amount The transaction amount
     * @param description The transaction description
     */
    public void notifyTransaction(ServerPlayerEntity player, String transactionType, long amount, String description) {
        if (!initialized || player == null) {
            return;
        }

        networkExecutor.execute(() -> {
            try {
                UUID playerId = player.getUuid();

                // Create transaction notification packet
                PacketByteBuf buf = PacketByteBufs.create();

                buf.writeUuid(playerId);
                buf.writeString(transactionType);
                buf.writeLong(amount);
                buf.writeString(description);
                buf.writeLong(System.currentTimeMillis());


                // Send packet
                NetworkManager.sendToPlayer(player, ECONOMY_TRANSACTION_NOTIFY, buf);

                Pokecobbleclaim.LOGGER.debug("Sent transaction notification to player " + player.getName().getString() +
                                           ": " + transactionType + " " + amount);

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error sending transaction notification to player " + player.getName().getString(), e);
            }
        });
    }
    /**
     * Sends a compact transfer result packet back to the initiating client for UI state changes.
     *
     * @param player The player to notify
     * @param resultCode 1=SUCCESS, 2=INSUFFICIENT_FUNDS, 0=ERROR
     * @param message Additional message for the client UI/logs
     */
    private void sendTransferResult(ServerPlayerEntity player, int resultCode, String message) {
        if (!initialized || player == null) {
            return;
        }
        networkExecutor.execute(() -> {
            try {
                PacketByteBuf buf = PacketByteBufs.create();
                buf.writeInt(resultCode);
                buf.writeString(message != null ? message : "");
                NetworkManager.sendToPlayer(player, ECONOMY_TRANSFER_RESULT, buf);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error sending transfer result to player " + player.getName().getString(), e);
            }
        });
    }


    /**
     * Handles balance request packets from clients.
     *
     * @param server The server instance
     * @param player The player who sent the request
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The response sender
     */
    private void handleBalanceRequest(net.minecraft.server.MinecraftServer server, ServerPlayerEntity player,
                                    net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                    PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender responseSender) {
        try {
            // Validate player
            PacketValidator.validatePlayer(player);

            // Balance requests don't need authentication as they're just requests for data
            // The response will be authenticated

            // Mark player as watching balance
            watchingPlayers.put(player.getUuid(), true);

            // Send current balance
            syncBalance(player);

            Pokecobbleclaim.LOGGER.debug("Processed balance request from player " + player.getName().getString());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling balance request from player " + player.getName().getString(), e);
        }
    }

    /**
     * Handles transfer request packets from clients (for bank app integration).
     *
     * @param server The server instance
     * @param player The player who sent the request
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The response sender
     */
    private void handleTransferRequest(net.minecraft.server.MinecraftServer server, ServerPlayerEntity player,
                                     net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                     PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender responseSender) {
        try {
            // Validate player
            PacketValidator.validatePlayer(player);

            // Read transfer data
            String targetPlayerName = buf.readString();
            long amount = buf.readLong();
            String description = buf.readString();

            // Basic sanity and anti-spam checks
            if (amount <= 0 || amount > EconomyConfig.MAX_TRANSACTION_AMOUNT) {
                notifyTransaction(player, "TRANSFER_ERROR", 0, "Invalid amount");
                sendTransferResult(player, 0, "Invalid amount");
                return;
            }
            UUID senderId = player.getUuid();
            if (isOnCooldown(lastTransferAt, senderId, TRANSFER_COOLDOWN_MS)) {
                notifyTransaction(player, "TRANSFER_ERROR", 0, "Please wait a moment");
                sendTransferResult(player, 0, "Please wait a moment");
                return;
            }
            markAction(lastTransferAt, senderId);

            // Find target player UUID
            UUID targetPlayerId = null;

            // First try online players
            ServerPlayerEntity targetPlayer = server.getPlayerManager().getPlayer(targetPlayerName);
            if (targetPlayer != null) {
                targetPlayerId = targetPlayer.getUuid();
            } else {
                // Try offline players using user cache
                try {
                    com.mojang.authlib.GameProfile profile = server.getUserCache().findByName(targetPlayerName).orElse(null);
                    if (profile != null) {
                        targetPlayerId = profile.getId();
                    }
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.debug("Could not find offline player: " + targetPlayerName);
                }
            }

            if (targetPlayerId == null) {
                // Send error notification to player
                notifyTransaction(player, "TRANSFER_ERROR", 0, "Player '" + targetPlayerName + "' not found");
                return;
            }

            // Create and execute transfer request
            com.pokecobble.economy.transaction.TransactionRequest request =
                com.pokecobble.economy.transaction.TransactionRequest.createTransfer(
                    player.getUuid(), targetPlayerId, amount, description);

            com.pokecobble.economy.transaction.TransactionResult result =
                economyManager.getTransactionManager().executeTransaction(request);

            if (result.isSuccess()) {
                // Notify both players
                notifyTransaction(player, "TRANSFER_SENT", amount, "Sent to " + targetPlayerName);
                if (targetPlayer != null) {
                    notifyTransaction(targetPlayer, "TRANSFER_RECEIVED", amount, "Received from " + player.getName().getString());
                }

                // Sync balances
                syncBalance(player);
                if (targetPlayer != null) {
                    syncBalance(targetPlayer);
                }

                // Inform initiator's UI for success page
                sendTransferResult(player, 1, "Transfer successful");

                Pokecobbleclaim.LOGGER.info("Transfer completed: " + amount + " from " + player.getName().getString() + " to " + targetPlayerName);
            } else {
                // Send error notification and inform UI for error page
                notifyTransaction(player, "TRANSFER_ERROR", 0, result.getMessage());

                // Map common error to insufficient funds result code if applicable
                int code = ("Insufficient balance".equalsIgnoreCase(result.getMessage())) ? 2 : 0;
                sendTransferResult(player, code, result.getMessage());

                Pokecobbleclaim.LOGGER.warn("Transfer failed: " + result.getMessage());
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling transfer request from player " + player.getName().getString(), e);
            notifyTransaction(player, "TRANSFER_ERROR", 0, "Transfer failed due to server error");
        }
    }

    /**
     * Handles food sale packets from clients (for farmer app integration).
     *
     * @param server The server instance
     * @param player The player who sent the request
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The response sender
     */
    private void handleFoodSale(net.minecraft.server.MinecraftServer server, ServerPlayerEntity player,
                               net.minecraft.server.network.ServerPlayNetworkHandler handler,
                               PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender responseSender) {
        try {
            // Validate player
            PacketValidator.validatePlayer(player);

            // Read enhanced sale data with validation info
            String productId = buf.readString();
            String productName = buf.readString();
            int requestedQuantity = buf.readInt();
            long expectedTotalValue = buf.readLong();
            int expectedPricePerItem = buf.readInt();
            String description = buf.readString();

            // Basic sanity and anti-spam checks
            if (requestedQuantity <= 0 || requestedQuantity > MAX_SALE_QUANTITY) {
                notifyTransaction(player, "SALE_ERROR", 0, "Invalid quantity");
                return;
            }
            UUID sellerId = player.getUuid();
            if (isOnCooldown(lastFoodSaleAt, sellerId, FOOD_SALE_COOLDOWN_MS)) {
                notifyTransaction(player, "SALE_ERROR", 0, "Please wait a moment");
                return;
            }
            markAction(lastFoodSaleAt, sellerId);

            // Server-side inventory validation - get the actual item from product manager
            net.minecraft.item.ItemStack targetItem = getItemStackFromProductId(productId);
            if (targetItem == null || targetItem.isEmpty()) {
                notifyTransaction(player, "SALE_ERROR", 0, "Invalid product: " + productName);
                Pokecobbleclaim.LOGGER.warn("Player {} tried to sell invalid product: {}", player.getName().getString(), productId);
                return;
            }

            // Count actual items in player's server-side inventory
            int actualQuantity = countItemsInInventory(player, targetItem);

            if (actualQuantity < requestedQuantity) {
                notifyTransaction(player, "SALE_ERROR", 0,
                    "You don't have enough " + productName + " to sell! (Have: " + actualQuantity + ", Need: " + requestedQuantity + ")");
                Pokecobbleclaim.LOGGER.warn("Player {} tried to sell {} {} but only has {} in inventory",
                    player.getName().getString(), requestedQuantity, productName, actualQuantity);
                return;
            }

            // Get current server-side price for validation
            int currentServerPrice = getCurrentServerPrice(productId);
            if (currentServerPrice <= 0) {
                notifyTransaction(player, "SALE_ERROR", 0, "Product price not available: " + productName);
                Pokecobbleclaim.LOGGER.warn("No server price available for product: {}", productId);
                return;
            }

            // Calculate actual transaction value using server price
            long actualTotalValue = (long) requestedQuantity * currentServerPrice;

            // Remove items from player's server-side inventory
            if (!removeItemsFromInventory(player, targetItem, requestedQuantity)) {
                notifyTransaction(player, "SALE_ERROR", 0, "Failed to remove items from inventory");
                Pokecobbleclaim.LOGGER.error("Failed to remove {} {} from {}'s inventory",
                    requestedQuantity, productName, player.getName().getString());
                return;
            }

            // Create and execute deposit request (adding money to player)
            com.pokecobble.economy.transaction.TransactionRequest request =
                com.pokecobble.economy.transaction.TransactionRequest.createDeposit(
                    player.getUuid(), actualTotalValue, description);

            com.pokecobble.economy.transaction.TransactionResult result =
                economyManager.getTransactionManager().executeTransaction(request);

            if (result.isSuccess()) {
                // Notify player of successful sale with actual values
                notifyTransaction(player, "FOOD_SALE", actualTotalValue,
                    "Sold " + requestedQuantity + " " + productName + " for " + actualTotalValue + " coins");

                // Sync balance
                syncBalance(player);

                Pokecobbleclaim.LOGGER.info("Food sale completed: {} sold {} {} for {} coins (server price: {} each)",
                    player.getName().getString(), requestedQuantity, productName, actualTotalValue, currentServerPrice);
            } else {
                // Transaction failed - restore items to inventory
                restoreItemsToInventory(player, targetItem, requestedQuantity);
                notifyTransaction(player, "SALE_ERROR", 0, "Transaction failed: " + result.getMessage());
                Pokecobbleclaim.LOGGER.warn("Food sale transaction failed for {}: {}", player.getName().getString(), result.getMessage());
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling food sale from player " + player.getName().getString(), e);
            notifyTransaction(player, "SALE_ERROR", 0, "Sale failed due to server error");
        }
    }

    /**
     * Gets ItemStack from product ID for server-side validation.
     */
    private net.minecraft.item.ItemStack getItemStackFromProductId(String productId) {
        try {
            // Try to get item from registry using product ID
            net.minecraft.util.Identifier itemId = new net.minecraft.util.Identifier(productId);
            net.minecraft.item.Item item = net.minecraft.registry.Registries.ITEM.get(itemId);
            if (item != null && item != net.minecraft.item.Items.AIR) {
                return new net.minecraft.item.ItemStack(item);
            }

            // Fallback: try common food items by name
            String itemName = productId.toLowerCase();
            if (itemName.contains("apple")) {
                return new net.minecraft.item.ItemStack(net.minecraft.item.Items.APPLE);
            } else if (itemName.contains("bread")) {
                return new net.minecraft.item.ItemStack(net.minecraft.item.Items.BREAD);
            } else if (itemName.contains("carrot")) {
                return new net.minecraft.item.ItemStack(net.minecraft.item.Items.CARROT);
            } else if (itemName.contains("potato")) {
                return new net.minecraft.item.ItemStack(net.minecraft.item.Items.POTATO);
            } else if (itemName.contains("beef")) {
                return new net.minecraft.item.ItemStack(net.minecraft.item.Items.COOKED_BEEF);
            } else if (itemName.contains("pork")) {
                return new net.minecraft.item.ItemStack(net.minecraft.item.Items.COOKED_PORKCHOP);
            }

            Pokecobbleclaim.LOGGER.warn("Could not find item for product ID: {}", productId);
            return null;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error getting ItemStack for product ID: " + productId, e);
            return null;
        }
    }

    /**
     * Counts items in player's server-side inventory.
     */
    private int countItemsInInventory(ServerPlayerEntity player, net.minecraft.item.ItemStack targetItem) {
        if (player == null || targetItem == null || targetItem.isEmpty()) {
            return 0;
        }

        int totalCount = 0;
        net.minecraft.entity.player.PlayerInventory inventory = player.getInventory();

        for (int i = 0; i < inventory.size(); i++) {
            net.minecraft.item.ItemStack stack = inventory.getStack(i);
            if (!stack.isEmpty() && net.minecraft.item.ItemStack.canCombine(stack, targetItem)) {
                totalCount += stack.getCount();
            }
        }

        return totalCount;
    }

    /**
     * Gets current server-side price for a product.
     */
    private int getCurrentServerPrice(String productId) {
        try {
            // Use client price manager as fallback since server-side manager doesn't exist yet
            var clientPriceManager = com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance();
            if (clientPriceManager != null) {
                int price = clientPriceManager.getCurrentPrice(productId);
                if (price > 0) {
                    return price;
                }
            }

            // Fallback to default prices based on product type
            String itemName = productId.toLowerCase();
            if (itemName.contains("apple")) return 5;
            if (itemName.contains("bread")) return 8;
            if (itemName.contains("carrot")) return 3;
            if (itemName.contains("potato")) return 4;
            if (itemName.contains("beef")) return 15;
            if (itemName.contains("pork")) return 12;

            // Default fallback price
            Pokecobbleclaim.LOGGER.warn("No price available for product: {}, using default", productId);
            return 10;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error getting server price for product: " + productId, e);
            return 10; // Default fallback price
        }
    }

    /**
     * Removes items from player's server-side inventory.
     */
    private boolean removeItemsFromInventory(ServerPlayerEntity player, net.minecraft.item.ItemStack targetItem, int quantityToRemove) {
        if (player == null || targetItem == null || targetItem.isEmpty() || quantityToRemove <= 0) {
            return false;
        }

        net.minecraft.entity.player.PlayerInventory inventory = player.getInventory();
        int remainingToRemove = quantityToRemove;

        for (int i = 0; i < inventory.size() && remainingToRemove > 0; i++) {
            net.minecraft.item.ItemStack stack = inventory.getStack(i);

            if (!stack.isEmpty() && net.minecraft.item.ItemStack.canCombine(stack, targetItem)) {
                int removeFromThisStack = Math.min(remainingToRemove, stack.getCount());
                stack.decrement(removeFromThisStack);
                remainingToRemove -= removeFromThisStack;

                // If stack is now empty, clear the slot
                if (stack.isEmpty()) {
                    inventory.setStack(i, net.minecraft.item.ItemStack.EMPTY);
                }
            }
        }

        // Mark inventory as changed to sync with client
        player.getInventory().markDirty();

        return remainingToRemove == 0; // Return true if all items were successfully removed
    }

    /**
     * Restores items to player's inventory (used when transaction fails).
     */
    private void restoreItemsToInventory(ServerPlayerEntity player, net.minecraft.item.ItemStack targetItem, int quantityToRestore) {
        if (player == null || targetItem == null || targetItem.isEmpty() || quantityToRestore <= 0) {
            return;
        }

        net.minecraft.entity.player.PlayerInventory inventory = player.getInventory();
        net.minecraft.item.ItemStack itemToAdd = targetItem.copy();

        int remainingToAdd = quantityToRestore;

        while (remainingToAdd > 0) {
            int stackSize = Math.min(remainingToAdd, itemToAdd.getMaxCount());
            net.minecraft.item.ItemStack stackToAdd = itemToAdd.copy();
            stackToAdd.setCount(stackSize);

            // Try to add to existing stacks first, then empty slots
            if (!inventory.insertStack(stackToAdd)) {
                // If inventory is full, drop items on ground
                player.dropItem(stackToAdd, false);
                Pokecobbleclaim.LOGGER.warn("Player {}'s inventory was full, dropped {} {} on ground",
                    player.getName().getString(), stackSize, targetItem.getItem().getName().getString());
            }

            remainingToAdd -= stackSize;
        }

        // Mark inventory as changed to sync with client
        player.getInventory().markDirty();
    }

    /**
     * Handles balance sync packets on the client side.
     *
     * @param client The client instance
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The response sender
     */
    @Environment(EnvType.CLIENT)
    private static void handleBalanceSync(net.minecraft.client.MinecraftClient client,
                                        net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                        PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender responseSender) {
        try {
            // Read packet data
            UUID playerId = buf.readUuid();
            long balance = buf.readLong();
            int dataVersion = buf.readInt();
            long timestamp = buf.readLong();

            // Skip authentication check for now - focus on getting balance sync working
            // TODO: Fix packet authentication system

            // Update client-side economy data
            com.pokecobble.economy.client.ClientEconomyManager.getInstance().updateBalance(playerId, balance, dataVersion);

            Pokecobbleclaim.LOGGER.debug("Received balance sync: " + balance + " for player " + playerId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling balance sync on client", e);
        }
    }

    /**
     * Handles transaction notification packets on the client side.
     *
     * @param client The client instance
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The response sender
     */
    @Environment(EnvType.CLIENT)
    private static void handleTransactionNotify(net.minecraft.client.MinecraftClient client,
                                              net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender responseSender) {
        try {
            // Read packet data
            UUID playerId = buf.readUuid();
            String transactionType = buf.readString();
            long amount = buf.readLong();
            String description = buf.readString();
            long timestamp = buf.readLong();

            // No heavy authentication: client UI notifications only

            // Handle transaction notification on client
            Pokecobbleclaim.LOGGER.debug("Received transaction notification: " + transactionType + " " + amount);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling transaction notification on client", e);
        }
    }

    /**
     * Handles economy data sync packets on the client side.
     *
     * @param client The client instance
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The response sender
     */
    @Environment(EnvType.CLIENT)
    private static void handleEconomyDataSync(net.minecraft.client.MinecraftClient client,
                                            net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                            PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender responseSender) {
        try {
            // Read packet data
            UUID playerId = buf.readUuid();
            long balance = buf.readLong();
            long totalEarned = buf.readLong();
            long totalSpent = buf.readLong();
            int transactionCount = buf.readInt();
            int dataVersion = buf.readInt();
            long lastUpdated = buf.readLong();

            // Simple client update; server already validated and authored values

            // Update client-side economy data
            com.pokecobble.economy.client.ClientEconomyManager.getInstance().updatePlayerData(
                playerId, balance, totalEarned, totalSpent, transactionCount, dataVersion, lastUpdated);

            Pokecobbleclaim.LOGGER.debug("Received economy data sync for player " + playerId +
                                       ": balance=" + balance + ", version=" + dataVersion);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling economy data sync on client", e);
        }
    }

    /**
     * Handles transfer result packets on the client side.
     *
     * @param client The client instance
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The response sender
     */
    @Environment(EnvType.CLIENT)
    private static void handleTransferResult(net.minecraft.client.MinecraftClient client,
                                           net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                           PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender responseSender) {
        try {
            // Read packet data
            int resultCode = buf.readInt();
            String message = buf.readString();

            // No heavy authentication: result is server-driven

            // Forward to bank app screen if it's open
            if (client.currentScreen instanceof com.pokecobble.phone.gui.BankAppScreen) {
                com.pokecobble.phone.gui.BankAppScreen bankScreen = (com.pokecobble.phone.gui.BankAppScreen) client.currentScreen;
                bankScreen.handleTransferResult(resultCode, message);
            }

            Pokecobbleclaim.LOGGER.debug("Processed transfer result: " + resultCode + " - " + message);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling transfer result", e);
        }
    }

    /**
     * Removes a player from tracking when they disconnect.
     *
     * @param playerId The player's UUID
     */
    public void removePlayer(UUID playerId) {
        playerDataVersions.remove(playerId);
        watchingPlayers.remove(playerId);
    }

    /**
     * Checks if a player is watching their balance.
     *
     * @param playerId The player's UUID
     * @return true if watching, false otherwise
     */
    public boolean isPlayerWatching(UUID playerId) {
        return watchingPlayers.getOrDefault(playerId, false);
    }

    /**
     * Gets network statistics.
     *
     * @return A map of statistics
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("initialized", initialized);
        stats.put("tracked_players", playerDataVersions.size());
        stats.put("watching_players", watchingPlayers.size());

        return stats;
    }

    /**
     * Checks if the network manager is initialized.
     *
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}
