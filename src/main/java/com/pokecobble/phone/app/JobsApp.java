package com.pokecobble.phone.app;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.client.ClientTownManager;
import com.pokecobble.town.client.NotificationRenderer;
import com.pokecobble.town.client.ClientTownJobsManager;
import com.pokecobble.town.gui.JobsScreen;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.Identifier;

/**
 * Jobs app that opens the Jobs screen if the player is in a town.
 * It mirrors the MyTownScreen behavior: if town data is missing but a townId exists,
 * it requests fresh town data and shows a loading note; also requests jobs data.
 */
public class JobsApp extends App {
    private static final Identifier ICON_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/jobapp/jobapp.png");

    public JobsApp() {
        super("jobs", "Jobs", ICON_TEXTURE);
    }

    @Override
    public void open(MinecraftClient client) {
        Pokecobbleclaim.LOGGER.debug("Opening Jobs app");
        if (client == null || client.player == null) {
            return;
        }

        // Check client-side town data
        Town playerTown = ClientTownManager.getInstance().getPlayerTown();
        java.util.UUID playerTownId = ClientTownManager.getInstance().getPlayerTownId();

        if (playerTown == null) {
            if (playerTownId != null) {
                // Mirror MyTownScreen: request fresh town data and show loading notification
                Pokecobbleclaim.LOGGER.info("JobsApp: Town cache missing, requesting fresh data for townId=" + playerTownId);
                com.pokecobble.town.network.town.TownNetworkHandler.requestTownData();
                NotificationRenderer.addNotification("Loading town data...");

                // Also request jobs data, so JobsScreen can load ASAP
                ClientTownJobsManager.getInstance().requestJobsData();

                // Open JobsScreen even while loading; it will show loading state
                client.execute(() -> client.setScreen(new JobsScreen(client.currentScreen, null)));
                return;
            }

            // No town at all
            NotificationRenderer.addNotification("You need to be in a town to use Jobs");
            return;
        }

        // Ensure we have current jobs data
        ClientTownJobsManager.getInstance().requestJobsData();

        // Open the Jobs screen with the current phone screen as parent
        client.execute(() -> client.setScreen(new JobsScreen(client.currentScreen, playerTown)));
    }
}

