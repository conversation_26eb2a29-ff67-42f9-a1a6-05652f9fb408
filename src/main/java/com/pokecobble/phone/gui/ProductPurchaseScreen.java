package com.pokecobble.phone.gui;

import com.pokecobble.economy.client.ClientEconomyManager;
import com.pokecobble.phone.network.ProductPurchasePacket;

import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;

/**
 * Rebuilt product purchase screen with consistent glass-style UI and economy integration.
 * Clean layout, clear quantity controls, live balance display, and accessible actions.
 */
public class ProductPurchaseScreen extends Screen {
    private final Screen parent;
    private final FarmerAppScreen.Product product;

    // Buttons
    private GlassButton decreaseButton;
    private GlassButton increaseButton;
    private GlassButton maxButton;
    private GlassButton purchaseButton;
    private GlassButton cancelButton;

    // Panel dimensions (responsive)
    private int panelWidth = 600;
    private int panelHeight = 420;

    // Glass palette (match town screens)
    private static final int GLASS_PANEL_BG = 0xD0101010;
    private static final int GLASS_HEADER_BG = 0x60404040;
    private static final int GLASS_CONTENT_BG = 0x30000000;
    private static final int GLASS_CARD_BG = 0x40303030;
    private static final int GLASS_CARD_HOVER = 0x60404040;
    private static final int GLASS_TOP_HIGHLIGHT = 0x20FFFFFF;
    private static final int GLASS_SHADOW = 0x40000000;

    // Text colors
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;
    private static final int TEXT_SECONDARY = 0xFFB8BCC8;
    private static final int TEXT_MUTED = 0xFF6C7293;
    private static final int TEXT_SUCCESS = 0xFF00C851;
    private static final int TEXT_ERROR = 0xFFFF4444;
    private static final int ACCENT_PRIMARY = 0xFF4CAF50;

    // Spacing
    private static final int SPACING_XS = 4;
    private static final int SPACING_SM = 8;
    private static final int SPACING_MD = 12;
    private static final int SPACING_LG = 16;
    private static final int HEADER_HEIGHT = 28;

    // State
    private int selectedQuantity = 1;
    private int maxQuantity = 64;
    private long cachedBalance = 0L;
    private int cachedLeftX;
    private int cachedTopY;

    public ProductPurchaseScreen(Screen parent, FarmerAppScreen.Product product) {
        super(Text.literal("Purchase: " + product.getName()));
        this.parent = parent;
        this.product = product;

        // Derive sensible max quantity by type
        switch (product.getType()) {
            case UPGRADE:
            case UNLOCK:
                maxQuantity = 1;
                break;
            case TOOL:
                maxQuantity = 5;
                break;
            default:
                maxQuantity = 64;
        }

        requestPlayerBalance();
    }

    @Override
    protected void init() {
        super.init();

        int targetWidth = Math.max(480, (int) (width * 0.55));
        panelWidth = Math.min(Math.min(targetWidth, 820), width - 40);
        int targetHeight = Math.max(300, (int) (height * 0.45));
        panelHeight = Math.min(Math.min(targetHeight, 440), height - 80);

        cachedLeftX = (width - panelWidth) / 2;
        cachedTopY = (height - panelHeight) / 2;

        // Buttons created each frame to reflect dynamic layout/state
        decreaseButton = null;
        increaseButton = null;
        maxButton = null;
        purchaseButton = null;
        cancelButton = null;
    }

    private void requestPlayerBalance() {
        try {
            net.minecraft.network.PacketByteBuf buf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.economy.network.EconomyNetworkManager.ECONOMY_BALANCE_REQUEST, buf
            );
        } catch (Exception e) {
            cachedBalance = 0L;
        }
    }

    private long getPlayerBalance() {
        try {
            long live = ClientEconomyManager.getInstance().getCurrentPlayerBalance();
            cachedBalance = live; // keep local for rendering math
            return live;
        } catch (Exception e) {
            return cachedBalance;
        }
    }

    private int getMaxAffordableQuantity() {
        long pricePer = product.getDiscountedPrice();
        if (pricePer <= 0) return maxQuantity;
        long balance = Math.max(getPlayerBalance(), 0);
        int affordable = (int) Math.min(Integer.MAX_VALUE, balance / pricePer);
        return Math.min(affordable, maxQuantity);
    }

    private void confirmPurchase() {
        if (selectedQuantity <= 0) return;
        long totalPrice = (long) product.getDiscountedPrice() * selectedQuantity;
        long balance = getPlayerBalance();
        if (totalPrice > balance) {
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(Text.literal("§cInsufficient funds! Need " + totalPrice + " coins, have " + balance), true);
            }
            return;
        }
        ProductPurchasePacket.sendPurchaseRequest(product.getId(), selectedQuantity, totalPrice);
        this.close();
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        renderBackground(context);

        // Guard against rare nulls during early lifecycle
        if (this.textRenderer == null) {
            super.render(context, mouseX, mouseY, delta);
            return;
        }

        int leftX = cachedLeftX;
        int topY = cachedTopY;

        drawGlassPanel(context, leftX, topY, panelWidth, panelHeight);
        drawHeader(context, leftX, topY, panelWidth);

        // Refresh balance each frame so UI stays in sync with economy syncs
        long balance = getPlayerBalance();

        int contentX = leftX + SPACING_LG;
        int contentY = topY + HEADER_HEIGHT + SPACING_MD;
        int contentWidth = panelWidth - SPACING_LG * 2;

        // Compute buttons Y to size content background
        int buttonHeight = 24;
        int buttonsY = topY + panelHeight - SPACING_LG - buttonHeight;

        // Content background area
        context.fill(contentX - SPACING_XS, contentY - SPACING_XS,
                     contentX + contentWidth + SPACING_XS, buttonsY - SPACING_MD,
                     GLASS_CONTENT_BG);

        // Product card
        int yCursor = contentY;
        drawProductInfo(context, contentX, yCursor, contentWidth);
        yCursor += 80 + SPACING_MD;

        // Quantity row
        int quantityRowHeight = 24;
        int controlsWidth = 240;
        int controlsX = contentX + (contentWidth - controlsWidth) / 2;
        decreaseButton = new GlassButton(controlsX, yCursor, 32, quantityRowHeight, "−", ButtonStyle.ICON, () -> {
            if (selectedQuantity > 1) selectedQuantity--;
        });
        increaseButton = new GlassButton(controlsX + 110, yCursor, 32, quantityRowHeight, "+", ButtonStyle.ICON, () -> {
            if (selectedQuantity < maxQuantity) selectedQuantity++;
        });
        maxButton = new GlassButton(controlsX + 146, yCursor, 62, quantityRowHeight, "Max", ButtonStyle.SECONDARY, () -> {
            selectedQuantity = Math.min(maxQuantity, getMaxAffordableQuantity());
        });

        decreaseButton.render(context, mouseX, mouseY);
        increaseButton.render(context, mouseX, mouseY);
        maxButton.render(context, mouseX, mouseY);

        // Quantity text centered between − and +
        String qtyText = "Qty: " + selectedQuantity + " / " + maxQuantity;
        int qtyTextX = controlsX + 34 + (110 - 34 - 32) / 2 - this.textRenderer.getWidth(qtyText) / 2;
        context.drawTextWithShadow(this.textRenderer, qtyText, qtyTextX, yCursor + (quantityRowHeight - 8) / 2, TEXT_SECONDARY);

        yCursor += quantityRowHeight + SPACING_MD;

        // Price calc
        drawPriceCalculation(context, contentX + contentWidth - 300, yCursor);
        yCursor += 14 + SPACING_MD;

        // Balance box
        drawBalanceInfo(context, contentX, yCursor, Math.min(300, contentWidth), balance);
        yCursor += 48 + SPACING_LG;

        // Action buttons (right-aligned)
        int buttonWidth = 120;
        int gap = SPACING_SM;
        int buyX = contentX + contentWidth - buttonWidth;
        int cancelX = buyX - gap - buttonWidth;
        cancelButton = new GlassButton(cancelX, buttonsY, buttonWidth, buttonHeight, "Cancel", ButtonStyle.TERTIARY, this::close);
        purchaseButton = new GlassButton(buyX, buttonsY, buttonWidth, buttonHeight, "Buy", ButtonStyle.PRIMARY, this::confirmPurchase);

        // Enable/disable buttons
        long totalPrice = (long) product.getDiscountedPrice() * selectedQuantity;
        boolean canAfford = totalPrice <= balance && selectedQuantity > 0;
        if (purchaseButton != null) purchaseButton.setEnabled(canAfford);
        if (decreaseButton != null) decreaseButton.setEnabled(selectedQuantity > 1);
        if (increaseButton != null) increaseButton.setEnabled(selectedQuantity < maxQuantity);
        if (maxButton != null) maxButton.setEnabled(getMaxAffordableQuantity() > selectedQuantity);

        cancelButton.render(context, mouseX, mouseY);
        purchaseButton.render(context, mouseX, mouseY);

        super.render(context, mouseX, mouseY, delta);
    }

    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        context.fill(x, y, x + width, y + height, GLASS_PANEL_BG);
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT);
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT);
        context.fill(x + width - 1, y, x + width, y + height, GLASS_TOP_HIGHLIGHT);
        context.fill(x, y + height - 1, x + width, y + height, GLASS_SHADOW);
        context.fill(x + 1, y + 1, x + width - 1, y + 2, GLASS_TOP_HIGHLIGHT);
    }

    private void drawHeader(DrawContext context, int x, int y, int width) {
        int headerX = x + SPACING_XS;
        int headerY = y + SPACING_XS;
        int headerWidth = width - SPACING_XS * 2;
        int headerHeight = HEADER_HEIGHT - SPACING_XS;
        context.fill(headerX, headerY, headerX + headerWidth, headerY + headerHeight, GLASS_HEADER_BG);
        context.fill(headerX, headerY, headerX + headerWidth, headerY + 1, GLASS_TOP_HIGHLIGHT);
        context.fill(headerX, headerY + headerHeight - 1, headerX + headerWidth, headerY + headerHeight, GLASS_SHADOW);
        String title = "💰 Purchase: " + product.getName();
        int titleX = headerX + (headerWidth - textRenderer.getWidth(title)) / 2;
        int titleY = headerY + (headerHeight - 8) / 2;
        context.drawTextWithShadow(textRenderer, title, titleX, titleY, TEXT_PRIMARY);
    }

    private void drawProductInfo(DrawContext context, int x, int y, int contentWidth) {
        int cardW = contentWidth;
        int cardH = 80;
        context.fill(x - SPACING_XS, y - SPACING_XS, x - SPACING_XS + cardW, y - SPACING_XS + cardH, GLASS_CARD_BG);
        context.fill(x - SPACING_XS, y - SPACING_XS, x - SPACING_XS + cardW, y - SPACING_XS + 1, GLASS_TOP_HIGHLIGHT);
        context.fill(x - SPACING_XS, y - SPACING_XS + cardH - 1, x - SPACING_XS + cardW, y - SPACING_XS + cardH, GLASS_SHADOW);

        String typeIcon = getProductTypeIcon(product.getType());
        context.drawTextWithShadow(textRenderer, typeIcon + "  " + product.getName(), x + SPACING_SM, y + 6, TEXT_PRIMARY);
        context.drawTextWithShadow(textRenderer, product.getDescription(), x + SPACING_SM, y + 6 + SPACING_MD, TEXT_SECONDARY);
        int priceColor = product.isOnSale() ? TEXT_SUCCESS : ACCENT_PRIMARY;
        context.drawTextWithShadow(textRenderer, "Price per item: " + product.getDiscountedPrice() + " coins",
            x + SPACING_SM, y + 6 + SPACING_MD * 2, priceColor);
    }

    private void drawPriceCalculation(DrawContext context, int x, int y) {
        long totalPrice = (long) product.getDiscountedPrice() * selectedQuantity;
        context.fill(x - SPACING_XS, y - SPACING_XS, x + 300, y + 1, GLASS_SHADOW);
        context.drawTextWithShadow(textRenderer,
            "Total Cost: " + selectedQuantity + " × " + product.getDiscountedPrice() + " = " + totalPrice + " coins",
            x, y + SPACING_XS, TEXT_PRIMARY);
    }

    private void drawBalanceInfo(DrawContext context, int x, int y, int boxWidth, long balance) {
        long totalPrice = (long) product.getDiscountedPrice() * selectedQuantity;
        boolean canAfford = totalPrice <= balance;
        int boxH = 48;
        int bx = x;
        int by = y;
        context.fill(bx, by, bx + boxWidth, by + boxH, GLASS_CARD_BG);
        context.fill(bx, by, bx + boxWidth, by + 1, GLASS_TOP_HIGHLIGHT);
        context.fill(bx, by + boxH - 1, bx + boxWidth, by + boxH, GLASS_SHADOW);

        context.drawTextWithShadow(textRenderer, "Balance: " + balance + " coins", bx + SPACING_SM, by + 6,
            canAfford ? TEXT_SUCCESS : TEXT_ERROR);
        if (canAfford) {
            long remaining = balance - totalPrice;
            context.drawTextWithShadow(textRenderer, "After purchase: " + remaining + " coins", bx + SPACING_SM, by + 22, TEXT_MUTED);
        } else {
            long needed = totalPrice - balance;
            context.drawTextWithShadow(textRenderer, "Need " + needed + " more coins", bx + SPACING_SM, by + 22, TEXT_ERROR);
        }
    }

    private String getProductTypeIcon(FarmerAppScreen.ProductType type) {
        switch (type) {
            case SEED: return "🌱";
            case FOOD: return "🥕";
            case TOOL: return "🔧";
            case UPGRADE: return "⬆️";
            case UNLOCK: return "🔓";
            default: return "📦";
        }
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        boolean handled = false;
        if (decreaseButton != null) handled |= decreaseButton.mouseClicked(mouseX, mouseY, button);
        if (increaseButton != null) handled |= increaseButton.mouseClicked(mouseX, mouseY, button);
        if (maxButton != null) handled |= maxButton.mouseClicked(mouseX, mouseY, button);
        if (purchaseButton != null) handled |= purchaseButton.mouseClicked(mouseX, mouseY, button);
        if (cancelButton != null) handled |= cancelButton.mouseClicked(mouseX, mouseY, button);
        return handled || super.mouseClicked(mouseX, mouseY, button);
    }

    private class GlassButton {
        private final int x;
        private final int y;
        private final int width;
        private final int height;
        private final String label;
        private final ButtonStyle style;
        private final Runnable onClick;
        private boolean enabled = true;

        GlassButton(int x, int y, int width, int height, String label, ButtonStyle style, Runnable onClick) {
            this.x = x; this.y = y; this.width = width; this.height = height;
            this.label = label; this.style = style; this.onClick = onClick;
        }

        void render(DrawContext context, int mouseX, int mouseY) {
            boolean hovered = mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
            int bg = (hovered && enabled) ? GLASS_CARD_HOVER : GLASS_CARD_BG;
            int textColor = enabled ? TEXT_PRIMARY : TEXT_SECONDARY;
            context.fill(x, y, x + width, y + height, bg);
            context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT);
            context.fill(x, y + height - 1, x + width, y + height, GLASS_SHADOW);

            // Primary style subtle emphasis
            if (style == ButtonStyle.PRIMARY && enabled) {
                context.fill(x, y, x + width, y + 2, GLASS_TOP_HIGHLIGHT);
            }

            if (textRenderer == null) return;
            int labelWidth = textRenderer.getWidth(label);
            int labelX = x + (width - labelWidth) / 2;
            int labelY = y + (height - 8) / 2;
            context.drawTextWithShadow(textRenderer, label, labelX, labelY, textColor);
        }

        boolean mouseClicked(double mouseX, double mouseY, int button) {
            if (button != 0) return false;
            if (enabled && mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height) {
                if (onClick != null) onClick.run();
                return true;
            }
            return false;
        }

        void setEnabled(boolean enabled) { this.enabled = enabled; }
    }

    private enum ButtonStyle { PRIMARY, SECONDARY, TERTIARY, ICON }

    @Override
    public void close() {
        if (this.client != null) this.client.setScreen(parent);
    }

    @Override
    public boolean shouldPause() { return false; }
}
