package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.TownBan;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Environment(EnvType.CLIENT)
public class TownBanClientManager {
    public interface BanListListener {
        void onBanListUpdated(UUID townId, List<TownBan> bans);
    }

    private static TownBanClientManager instance;

    private final Map<UUID, List<TownBan>> townIdToBans = new ConcurrentHashMap<>();
    private final Set<BanListListener> listeners = ConcurrentHashMap.newKeySet();

    public static TownBanClientManager getInstance() {
        if (instance == null) {
            instance = new TownBanClientManager();
        }
        return instance;
    }

    public void updateBanList(UUID townId, List<TownBan> bans) {
        if (townId == null) return;
        townIdToBans.put(townId, new ArrayList<>(bans));
        for (BanListListener listener : listeners) {
            try {
                listener.onBanListUpdated(townId, new ArrayList<>(bans));
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error notifying ban list listener: " + e.getMessage());
            }
        }
    }

    public List<TownBan> getBans(UUID townId) {
        List<TownBan> list = townIdToBans.get(townId);
        return list != null ? new ArrayList<>(list) : Collections.emptyList();
    }

    public void addListener(BanListListener listener) {
        if (listener != null) listeners.add(listener);
    }

    public void removeListener(BanListListener listener) {
        if (listener != null) listeners.remove(listener);
    }
}



