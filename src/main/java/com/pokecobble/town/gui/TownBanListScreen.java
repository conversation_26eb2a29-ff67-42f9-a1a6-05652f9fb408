package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownBan;
import com.pokecobble.town.client.ClientTownManager;
import com.pokecobble.town.client.TownBanClientManager;
import com.pokecobble.town.network.town.TownNetworkHandler;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;

import java.util.List;
import java.util.UUID;

@Environment(EnvType.CLIENT)
public class TownBanListScreen extends Screen implements TownBanClientManager.BanListListener {
    private final Screen parent;
    private int panelWidth = 650;
    private int panelHeight = 420;

    // Match MyTownScreen/CreateTownScreen glass palette
    private static final int GLASS_PANEL_BG = 0xD0101010;
    private static final int GLASS_HEADER_BG = 0x60404040;
    private static final int GLASS_CARD_BG = 0x40303030;
    private static final int GLASS_CARD_HOVER = 0x60404040;
    private static final int GLASS_BORDER = 0x40FFFFFF;
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;

    private int contentX;
    private int contentY;
    private int contentWidth;
    private int contentHeight;

    private int scrollOffset = 0;
    private int rowHeight = 28;

    private List<TownBan> currentBans = java.util.Collections.emptyList();
    private UUID currentTownId;

    public TownBanListScreen(Screen parent) {
        super(Text.literal("Town Bans"));
        this.parent = parent;
    }

    @Override
    protected void init() {
        super.init();
        calculateLayout();
        TownBanClientManager.getInstance().addListener(this);

        Town town = ClientTownManager.getInstance().getPlayerTown();
        if (town != null) {
            currentTownId = town.getId();
            currentBans = TownBanClientManager.getInstance().getBans(currentTownId);
            TownNetworkHandler.requestBanList();
        }
    }

    private void calculateLayout() {
        panelWidth = Math.min(width - 20, 800);
        panelHeight = Math.min(height - 20, 520);
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;
        contentX = leftX + 6;
        contentY = topY + 28;
        contentWidth = panelWidth - 12;
        contentHeight = panelHeight - 34;
    }

    @Override
    public void resize(MinecraftClient client, int width, int height) {
        super.resize(client, width, height);
        calculateLayout();
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context);

        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // Panel
        drawGlassPanel(context, leftX, topY, panelWidth, panelHeight);
        drawGlassHeader(context, leftX, topY, panelWidth, "🚫 Banned Players");

        // Content card
        drawGlassCard(context, contentX, contentY, contentWidth, contentHeight, "Manage bans");

        // Clip scrolling area inside the card body
        int listX = contentX + 6;
        int listY = contentY + 20;
        int listW = contentWidth - 12;
        int listH = contentHeight - 26;

        context.enableScissor(listX, listY, listX + listW, listY + listH);

        int y = listY - scrollOffset;
        for (int i = 0; i < currentBans.size(); i++) {
            TownBan ban = currentBans.get(i);
            int rowY = y + i * rowHeight;
            if (rowY + rowHeight < listY || rowY > listY + listH) continue;

            boolean hovered = mouseX >= listX && mouseX <= listX + listW && mouseY >= rowY && mouseY <= rowY + rowHeight;
            int bg = hovered ? GLASS_CARD_HOVER : 0x00000000;
            if (bg != 0) context.fill(listX, rowY, listX + listW, rowY + rowHeight, bg);

            // Left: player name and reason
            String name = ban.getBannedPlayerName() != null ? ban.getBannedPlayerName() : ban.getBannedPlayerId().toString();
            String reason = ban.getReason();
            context.drawTextWithShadow(this.textRenderer, name, listX + 6, rowY + 6, TEXT_PRIMARY);
            context.drawTextWithShadow(this.textRenderer, "Reason: " + reason, listX + 6, rowY + 16, TEXT_SECONDARY);

            // Right: duration and Unban button area
            String duration = ban.isPermanent() ? "Permanent" : ban.getBanDurationString();
            int durationWidth = this.textRenderer.getWidth(duration);
            int durationX = listX + listW - 6 - 60 - 8 - durationWidth;
            context.drawTextWithShadow(this.textRenderer, duration, durationX, rowY + 10, TEXT_SECONDARY);

            int btnW = 60;
            int btnH = 16;
            int btnX = listX + listW - btnW - 6;
            int btnY = rowY + (rowHeight - btnH) / 2;
            boolean btnHover = mouseX >= btnX && mouseX <= btnX + btnW && mouseY >= btnY && mouseY <= btnY + btnH;
            drawGlassButton(context, btnX, btnY, btnW, btnH, btnHover);
            context.drawCenteredTextWithShadow(this.textRenderer, "Unban", btnX + btnW / 2, btnY + 4, TEXT_PRIMARY);

            // Store per-row unban button bounds via implicit checks in mouseClicked
        }

        context.disableScissor();

        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double verticalAmount) {
        int maxOffset = Math.max(0, currentBans.size() * rowHeight - (contentHeight - 26));
        scrollOffset = (int) Math.max(0, Math.min(maxOffset, scrollOffset - verticalAmount * 20));
        return true;
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) {
            // Check unban button clicks
            int listX = contentX + 6;
            int listY = contentY + 20;
            int listW = contentWidth - 12;
            int y = listY - scrollOffset;
            for (int i = 0; i < currentBans.size(); i++) {
                int rowY = y + i * rowHeight;
                if (rowY + rowHeight < listY || rowY > listY + (contentHeight - 26)) continue;
                int btnW = 60;
                int btnH = 16;
                int btnX = listX + listW - btnW - 6;
                int btnY = rowY + (rowHeight - btnH) / 2;
                if (mouseX >= btnX && mouseX <= btnX + btnW && mouseY >= btnY && mouseY <= btnY + btnH) {
                    TownBan ban = currentBans.get(i);
                    TownNetworkHandler.requestUnbanPlayer(ban.getBannedPlayerId());
                    return true;
                }
            }
        }
        return super.mouseClicked(mouseX, mouseY, button);
    }

    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        context.fill(x, y, x + width, y + height, GLASS_PANEL_BG);
        context.fill(x, y, x + width, y + 1, GLASS_BORDER);
        context.fill(x, y, x + 1, y + height, GLASS_BORDER);
        context.fill(x + width - 1, y, x + width, y + height, 0x20FFFFFF);
        context.fill(x, y + height - 1, x + width, y + height, 0x40000000);
    }

    private void drawGlassHeader(DrawContext context, int x, int y, int width, String title) {
        context.fill(x, y, x + width, y + 24, GLASS_HEADER_BG);
        int titleY = y + (24 - 8) / 2;
        int titleX = x + (width - this.textRenderer.getWidth(title)) / 2;
        context.drawTextWithShadow(this.textRenderer, title, titleX, titleY, TEXT_PRIMARY);
        context.fill(x, y + 24 - 1, x + width, y + 24, GLASS_BORDER);
    }

    private void drawGlassCard(DrawContext context, int x, int y, int width, int height, String title) {
        context.fill(x, y, x + width, y + height, GLASS_CARD_BG);
        context.fill(x, y, x + width, y + 1, GLASS_BORDER);
        context.fill(x, y, x + 1, y + height, GLASS_BORDER);
        context.fill(x + width - 1, y, x + width, y + height, 0x20FFFFFF);
        context.fill(x, y + height - 1, x + width, y + height, 0x40000000);
        context.drawTextWithShadow(this.textRenderer, title, x + 8, y + 4, TEXT_PRIMARY);
        context.fill(x + 8, y + 14, x + width - 8, y + 15, GLASS_BORDER);
    }

    private void drawGlassButton(DrawContext context, int x, int y, int width, int height, boolean hover) {
        int color = hover ? 0xFF4A90E2 : GLASS_CARD_BG;
        context.fill(x, y, x + width, y + height, color);
        context.fill(x, y, x + width, y + 1, GLASS_BORDER);
        context.fill(x, y, x + 1, y + height, GLASS_BORDER);
        context.fill(x + width - 1, y, x + width, y + height, 0x20FFFFFF);
        context.fill(x, y + height - 1, x + width, y + height, 0x40000000);
    }

    @Override
    public void close() {
        client.setScreen(parent);
    }

    @Override
    public void onBanListUpdated(UUID townId, List<TownBan> bans) {
        if (currentTownId != null && currentTownId.equals(townId)) {
            this.currentBans = bans;
        }
    }
}


